# DEMO SCRIPT CHI TIẾT - BOND HUB

## 🎯 **CHUẨN BỊ DEMO**

### **Môi trường Demo:**
- **Backend:** Đảm bảo server đang chạy ổn định
- **Database:** Có sẵn data mẫu (users, groups, messages)
- **Mobile App:** Build version stable trên device
- **Web App:** Browser đã mở sẵn tabs cần thiết

### **Tài khoản Demo:**
```
User 1: +84901234567 (Admin demo)
User 2: +84901234568 (Member demo)  
User 3: +84901234569 (Friend demo)
OTP Demo: 123456 (cho tất cả số)
```

---

## 📱 **DEMO MOBILE APP (8 phút)**

### **1. Đăng ký/Đăng nhập (1.5 phút)**

**Script:**
> "<PERSON><PERSON><PERSON> tiên, chúng em sẽ demo quá trình đăng ký tài khoản mới."

**Actions:**
1. Mở app → <PERSON>àn hình Welcome
2. Tap "Đăng ký" 
3. Nhập số điện thoại: `+84901234570`
4. Tap "Gửi mã OTP"
5. Nhập OTP: `123456`
6. Điền thông tin:
   - Họ tên: "Demo User"
   - Email: "<EMAIL>"
7. Tap "Hoàn tất đăng ký"

**Giải thích:**
> "Hệ thống sử dụng OTP qua SMS để xác thực số điện thoại. Sau khi xác thực thành công, user sẽ được tự động đăng nhập và chuyển đến màn hình chính."

### **2. Giao diện chính (1 phút)**

**Script:**
> "Giao diện chính có 3 tab chính:"

**Actions:**
1. **Tab Conversations:**
   - Hiển thị danh sách cuộc trò chuyện
   - Recent messages preview
   - Unread message count
   - Online status indicators

2. **Tab Contacts:**
   - Danh sách bạn bè
   - Friend requests
   - Danh bạ máy đã sync

3. **Tab Settings:**
   - Profile settings
   - Privacy settings
   - Notification settings

**Giải thích:**
> "UI được thiết kế theo Material Design principles, đảm bảo trải nghiệm nhất quán và intuitive."

### **3. Chat cá nhân (2.5 phút)**

**Script:**
> "Bây giờ chúng em sẽ demo tính năng chat cá nhân."

**Actions:**
1. **Tạo chat mới:**
   - Tap "+" button
   - Chọn "Tìm bạn bè"
   - Search: "Demo Friend"
   - Tap để start conversation

2. **Gửi tin nhắn text:**
   - Type: "Xin chào! Đây là demo Bond Hub"
   - Tap Send
   - Hiển thị message status: Sent → Delivered → Read

3. **Gửi media:**
   - Tap attachment icon
   - Chọn "Camera" → Chụp ảnh
   - Add caption: "Demo image"
   - Send

4. **AI Enhancement:**
   - Type: "tôi muốn hỏi về dự án"
   - Long press message
   - Chọn "Cải thiện với AI"
   - AI suggest: "Tôi muốn tìm hiểu thêm về dự án này. Bạn có thể chia sẻ thêm thông tin được không?"
   - Chọn "Sử dụng"

**Giải thích:**
> "Tính năng AI enhancement sử dụng Google Gemini để cải thiện tone và grammar của tin nhắn, giúp giao tiếp chuyên nghiệp hơn."

### **4. Chat nhóm (2 phút)**

**Script:**
> "Tiếp theo là demo tính năng chat nhóm."

**Actions:**
1. **Tạo nhóm:**
   - Tap "+" → "Tạo nhóm"
   - Tên nhóm: "Demo Group"
   - Chọn members từ contact list
   - Upload group avatar
   - Tap "Tạo nhóm"

2. **Group management:**
   - Tap group name → Group Info
   - Add member: Tap "Thêm thành viên"
   - Change role: Long press member → "Đặt làm admin"
   - Group settings: Đổi tên, avatar, description

3. **Group chat:**
   - Send message: "Chào mọi người!"
   - @mention: "@DemoUser bạn có ở đây không?"
   - Reply to message: Long press → Reply

**Giải thích:**
> "Hệ thống hỗ trợ role-based permissions với Admin có thể manage members, change group settings, và Member chỉ có thể chat."

### **5. Video Call (1 phút)**

**Script:**
> "Cuối cùng là demo tính năng video call."

**Actions:**
1. **Khởi tạo call:**
   - Trong chat → Tap video call icon
   - Waiting room hiển thị
   - Play waiting sound

2. **Trong cuộc gọi:**
   - Camera on/off toggle
   - Microphone mute/unmute
   - Switch camera (front/back)
   - End call

**Giải thích:**
> "Video call sử dụng WebRTC technology cho peer-to-peer connection, đảm bảo chất lượng cao và độ trễ thấp."

---

## 💻 **DEMO WEB APP (3 phút)**

### **1. Responsive Design (1 phút)**

**Script:**
> "Web app có giao diện tương tự mobile nhưng được tối ưu cho desktop."

**Actions:**
1. Mở browser → Navigate to app URL
2. Login với tài khoản demo
3. Resize window để show responsive design
4. Demo keyboard shortcuts:
   - `Ctrl + K`: Quick search
   - `Enter`: Send message
   - `Esc`: Close modals

### **2. Advanced Features (1 phút)**

**Actions:**
1. **Multi-window support:**
   - Open multiple chat windows
   - Drag & drop files
   - Copy/paste images

2. **Desktop notifications:**
   - Receive message → Browser notification
   - Click notification → Focus window

### **3. Sync Across Devices (1 phút)**

**Script:**
> "Cuối cùng, chúng em demo real-time sync giữa mobile và web."

**Actions:**
1. Send message từ mobile
2. Message xuất hiện real-time trên web
3. Mark as read trên web
4. Read status update trên mobile
5. Type trên mobile → Typing indicator trên web

**Giải thích:**
> "Tất cả data được sync real-time qua WebSocket connection, đảm bảo consistent experience across platforms."

---

## 🎯 **DEMO TIPS & TRICKS**

### **Nếu Demo Bị Lỗi:**
1. **Network issue:** Có backup screen recording
2. **App crash:** Restart app nhanh, explain calmly
3. **Server down:** Switch to local demo environment
4. **Feature not working:** Explain expected behavior

### **Engagement Techniques:**
1. **Ask audience:** "Ai đã từng sử dụng app nhắn tin nào?"
2. **Interactive demo:** "Bạn nào muốn thử gửi tin nhắn?"
3. **Compare features:** "So với WhatsApp/Telegram, Bond Hub có..."

### **Technical Explanations:**
- Luôn explain WHY chọn technology đó
- Mention performance benefits
- Highlight security features
- Show code snippets nếu cần

---

## 📋 **DEMO CHECKLIST**

### **Trước Demo:**
- [ ] Test tất cả features hoạt động
- [ ] Clear cache và restart apps
- [ ] Prepare backup demo data
- [ ] Check internet connection
- [ ] Volume settings appropriate

### **Trong Demo:**
- [ ] Speak clearly và không quá nhanh
- [ ] Explain mỗi action đang làm
- [ ] Highlight key features
- [ ] Handle errors gracefully
- [ ] Keep eye contact với audience

### **Sau Demo:**
- [ ] Summarize key points
- [ ] Ask for questions
- [ ] Provide additional context nếu cần

---

## 🚀 **BACKUP PLANS**

### **Plan A:** Live Demo (Preferred)
- Real-time interaction
- Show actual performance
- Answer questions immediately

### **Plan B:** Screen Recording
- Pre-recorded perfect demo
- No risk of technical issues
- Can pause for explanations

### **Plan C:** Screenshots + Explanation
- Static images with detailed explanation
- Focus more on architecture và code
- Still effective for showing features

---

**💡 Remember: Confidence is key! Even if something goes wrong, stay calm and professional.**
